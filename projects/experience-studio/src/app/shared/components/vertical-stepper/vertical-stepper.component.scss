.vertical-stepper {
  // FEATURE 2: Consistent animation timing variables
  --stepper-animation-duration: 0.5s;
  --stepper-animation-timing: cubic-bezier(0.25, 0.1, 0.25, 1);
  --stepper-line-animation-duration: 1.5s;
  --stepper-line-animation-timing: cubic-bezier(0.4, 0, 0.2, 1);

  width: 100%;
  margin: 16px auto;
  border-radius: 8px;
  box-sizing: border-box;
  animation: fadeIn var(--stepper-animation-duration) ease-in-out;


  &.light {
    background-color: var(--chat-window-card-bg-color) !important;
    box-shadow: none;
  }


  &.dark {
    background-color: var(--chat-window-card-bg-color) !important;
    box-shadow: none;
  }


  .stepper-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    align-items: flex-start; /* Left align the stepper */

    // Empty state placeholder styling removed
  }


  .stepper-item {
    display: flex;
    position: relative;
    transition: all var(--stepper-animation-duration) var(--stepper-animation-timing);
    align-self: flex-start; /* Left align each step */
    width: 100%;
    will-change: transform, opacity;
    transform: translateZ(0); /* Force GPU acceleration */


    &.hidden {
      display: none;
    }


    &.future {
      opacity: 0;
      height: 0;
      transform: translateY(-20px);
      overflow: hidden;
    }


    // Removed next step styling - next steps are no longer displayed


    &.completed {
      .step-circle {
        background-color: #9c27b0;
        border: none;
        color: white;
        cursor: pointer;
        transition: all var(--stepper-animation-duration) var(--stepper-animation-timing);
        box-shadow: 0 2px 4px rgba(156, 39, 176, 0.3);
      }


      .step-line {
        background: linear-gradient(180deg, #9c27b0 0%, #e91e63 100%);
      }


      .step-title {
        cursor: pointer;
        transition: all var(--stepper-animation-duration) var(--stepper-animation-timing);


        .light & {
          color: var(--color-primary, #6b46c1);
        }


        .dark & {
          color: var(--color-primary-light, #9f7aea);
        }
      }


      // Allow interaction with all steps
      &.in-progress-mode {
        .step-circle,
        .step-title {
          cursor: pointer !important;
          pointer-events: auto;
        }
      }
    }


    &.active {
      .step-circle {
        border: none;
        color: white;
        transition: all var(--stepper-animation-duration) var(--stepper-animation-timing);
        box-shadow: none;
      }


      .step-title {
        font-weight: 600;


        .light & {
          color: var(--color-primary, #6b46c1);
        }


        .dark & {
          color: var(--color-primary-light, #9f7aea);
        }
      }
    }
  }


  .step-line-container {
    position: absolute;
    left: 11px;
    top: 30px; /* Gap between tick and line */
    bottom: -20px;
    width: 2px;
    z-index: 0;
    height: calc(100% - 10px); /* Fixed height to ensure consistency */
    opacity: 1; /* Always fully visible, no fading */
    visibility: visible; /* Always visible */
    will-change: height; /* Only animate height, not opacity */
    transform: translateZ(0); /* Force GPU acceleration */


    .light & {
      background-color: #e9ecef;
    }


    .dark & {
      background-color: #555;
    }


    /* Hide line when step is collapsed */
    &.hidden-line {
      opacity: 0;
      visibility: hidden;
    }
  }


  .step-line {
    position: absolute;
    top: 0;
    width: 100%;
    height: 0;
    background: linear-gradient(180deg, #9c27b0 0%, #e91e63 100%);
    transition: height var(--stepper-line-animation-duration) var(--stepper-line-animation-timing);
    opacity: 1; /* Always fully visible, no fading */


    &.completed {
      height: 100%;
    }


    &.animating {
      animation: connectLine var(--stepper-line-animation-duration) var(--stepper-line-animation-timing) forwards;
    }


    /* Show the line animation when step is expanded */
    &.expanding {
      animation: connectLine var(--stepper-line-animation-duration) var(--stepper-line-animation-timing) forwards;
    }
  }


  .step-circle {
    z-index: 10;
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--stepper-animation-duration) var(--stepper-animation-timing);
    margin-right: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transform: scale(1);
    position: relative;
    .light & {
      border: none;
      background-color: #f8f9fa;
    }


    .dark & {
      background-color: #333;
      border: none;
    }


    &.completed {
      border: none;
      background-color: transparent !important;
      box-shadow: none;
    }


    // When step is active, make the background transparent to show the spinner
    &.active {
      background-color: transparent !important;
    }


    // When step is failed, make the background transparent to show the X icon
    &.failed {
      background-color: transparent !important;
      box-shadow: none;
      border: none;
      color: white;
      cursor: pointer;
      transition: all var(--stepper-animation-duration) var(--stepper-animation-timing);


      // Allow interaction with all steps
      .stepper-item.in-progress-mode & {
        cursor: pointer !important;
        pointer-events: auto;
      }
    }


    // Allow interaction with processing steps
    &.processing {
      cursor: pointer !important;
      pointer-events: auto;
    }


    // Make clickable steps visually distinct
    &.clickable {
      cursor: pointer !important;
    }

    // FEATURE 1: Non-collapsible processing step styling
    &.non-collapsible {
      cursor: default !important;
      opacity: 1 !important;
    }
  }


  .step-icon {
    width: 24px;
    height: 24px;
    position: absolute;
    top: 0;
    left: 0;
    animation: fadeInScale 0.3s ease-out;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
    z-index: 20; // Ensure icons are always on top
  }


  @keyframes fadeInScale {
    from {
      opacity: 0;
      transform: scale(0.8);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }


  .step-number {
    font-weight: 600;
    font-size: 16px;


    .light & {
      color: var(--text-secondary, #4a5568);
    }


    .dark & {
      color: var(--text-secondary-dark, #e2e8f0);
    }
  }


  .active .step-number {
    .light & {
      color: var(--color-primary, #6b46c1);
    }


    .dark & {
      color: var(--color-primary-light, #9f7aea);
    }
  }


  .step-content {
    flex: 1;
    padding-top: 2px;
    min-width: 0; // Allow flex item to shrink
    overflow: visible; // Prevent content clipping
    word-wrap: break-word;
    overflow-wrap: break-word;
  }


  .step-content-inner {
    width: 100%;
    overflow: visible;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }


  .step-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    transition: color var(--stepper-animation-duration) var(--stepper-animation-timing);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    // align-items: center;


    // Allow interaction with all steps
    .stepper-item.active:not(.completed) &,
    .stepper-item.in-progress-mode & {
      cursor: pointer !important;
      pointer-events: auto;
    }


    // Make clickable titles visually distinct
    &.clickable {
      cursor: pointer !important;
    }


    .light & {
      color: var(--text-primary, #2d3748);
    }


    .dark & {
      color: var(--text-primary-dark, #f8f9fa);
    }


    &:hover {
      .light & {
        color: var(--color-primary, #6b46c1);
      }


      .dark & {
        color: var(--color-primary-light, #9f7aea);
      }
    }


    /* Accordion indicator removed */
  }


  // Retry button for failed steps - styled to match the image
  .step-retry-button {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #7e3af2; // Purple color from the image
    border: none;
    border-radius: 50%; // Circular button
    padding: 6px;
    width: 32px;
    height: 32px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 8px;
    position: relative;
    color: white;
    z-index: 20; // Ensure button is above other elements
    box-shadow: 0 2px 8px rgba(126, 58, 242, 0.4); // Purple shadow


    // Light theme styles
    .light & {
      background-color: #7e3af2; // Purple color
      color: white;


      &:hover {
        background-color: #6929c4; // Darker purple on hover
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(126, 58, 242, 0.5);
      }


      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(126, 58, 242, 0.3);
      }
    }


    // Dark theme styles
    .dark & {
      background-color: #7e3af2; // Same purple color for dark theme
      color: white;


      &:hover {
        background-color: #8b5cf6; // Lighter purple on hover for dark theme
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.5);
      }


      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
      }
    }


    // SVG icon styling
    svg {
      width: 18px;
      height: 18px;
      fill: white;
      // Add a slight rotation to the icon for better visual appearance
      transform: rotate(-10deg);
    }


    // Prevent event propagation to parent elements
    &:focus {
      outline: none;
    }


    // Add pulse animation on hover
    &:hover svg {
      animation: pulse-icon 1s infinite alternate;
    }


    // Add shake animation on click
    &:active {
      animation: shake-button 0.5s ease-in-out;
    }
  }


  // Pulse animation for the icon
  @keyframes pulse-icon {
    0% {
      transform: rotate(-10deg) scale(1);
    }
    100% {
      transform: rotate(-10deg) scale(1.2);
    }
  }


  // Shake animation for retry button
  @keyframes shake-button {
    0%,
    100% {
      transform: translateX(0);
    }
    10%,
    30%,
    50%,
    70%,
    90% {
      transform: translateX(-2px);
    }
    20%,
    40%,
    60%,
    80% {
      transform: translateX(2px);
    }
  }


  .step-description {
    font-size: 14px;
    margin-bottom: 8px;
    transition:
      max-height var(--stepper-animation-duration) var(--stepper-animation-timing),
      opacity var(--stepper-animation-duration) var(--stepper-animation-timing),
      margin var(--stepper-animation-duration) var(--stepper-animation-timing),
      filter var(--stepper-animation-duration) var(--stepper-animation-timing);
    max-height: none; // Remove height constraint to prevent overflow
    opacity: 1;
    overflow: visible; // Allow content to expand naturally
    will-change: max-height, opacity, margin, filter;
    transform: translateZ(0); /* Force GPU acceleration */
    word-wrap: break-word;
    overflow-wrap: break-word;
    filter: blur(0px); // Default sharp state


    .light & {
      color: var(--text-secondary, #4a5568);
    }


    .dark & {
      color: var(--text-secondary-dark, #cbd5e0);
    }


    // Expanding state - starts blurred and becomes sharp
    &.expanding {
      filter: blur(4px);
      opacity: 0;
      max-height: 0;
      margin-top: 0;
      margin-bottom: 0;
      overflow: hidden;
      animation: expandBlurToFocus var(--stepper-animation-duration) var(--stepper-animation-timing) forwards;
    }

    // Expanded state - fully sharp and visible
    &.expanded {
      filter: blur(0px);
      opacity: 1;
      max-height: none;
      overflow: visible;
    }

    // Collapsing state - becomes blurred as it collapses
    &.collapsing {
      animation: collapseSharpToBlur var(--stepper-animation-duration) var(--stepper-animation-timing) forwards;
    }

    // Collapsed state - hidden and blurred
    &.collapsed {
      max-height: 0;
      opacity: 0;
      margin-top: 0;
      margin-bottom: 0;
      overflow: hidden; // Only hide overflow when collapsed
      filter: blur(3px);
      transition:
        max-height var(--stepper-animation-duration) var(--stepper-animation-timing),
        opacity var(--stepper-animation-duration) var(--stepper-animation-timing),
        margin var(--stepper-animation-duration) var(--stepper-animation-timing),
        filter var(--stepper-animation-duration) var(--stepper-animation-timing);
    }


    ::ng-deep {
      p {
        margin: 0 0 8px 0;
      }


      ul,
      ol {
        margin: 8px 0;
        padding-left: 20px;
      }
    }
  }


  .step-items {
    list-style-type: disc;
    margin-top: 8px;
    margin-left: 20px;
    color: #4a5568;


    .dark & {
      color: #cbd5e0;
    }
  }


  .step-item {
    margin-bottom: 4px;
    font-size: 14px;
  }


  .modern-loading-spinner {
    width: 24px;
    height: 24px;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;


    .spinner-ring {
      position: absolute;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #6566cd;
      border-bottom-color: #e30a6d;
      filter: drop-shadow(0 0 1px rgba(101, 102, 205, 0.3));
      animation: spin-ring 1.5s ease-in-out infinite;
    }


    .spinner-core {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: linear-gradient(135deg, #6566cd 0%, #e30a6d 100%);
      box-shadow: 0 0 10px rgba(229, 10, 109, 0.5);
      animation: pulse 1.5s ease-in-out infinite alternate;
    }
  }


  @keyframes spin-ring {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }


  @keyframes pulse {
    0% {
      transform: scale(0.8);
      opacity: 1; /* Remove low opacity - maintain full visibility */
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }


  // Removed .step-next and .next-title styles - next steps are no longer displayed


  .restart-button-container {
    margin-top: 40px;
    display: flex;
    justify-content: center;
  }


  // Timer styles - positioned at the right end of the step title
  .step-timer {
    margin-left: auto;
    background: transparent;
    color: var(--code-viewer-text);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    white-space: nowrap;
    flex-shrink: 0;
    .light & {
      background: rgba(0, 0, 0, 0.85);
      color: #ffffff;
      border: 1px solid rgba(255, 255, 255, 0.15);
    }


    .dark & {
      background: rgba(0, 0, 0, 0.9);
      color: #ffffff;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }


    // Subtle animation
    animation: timerFadeIn 0.3s ease-out;
  }


  @keyframes timerFadeIn {
    from {
      opacity: 0;
      transform: scale(0.5);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }


  .restart-button {
    padding: 12px 24px;
    background-color: #6b46c1;
    color: white;
    font-weight: 600;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;


    &:hover {
      background-color: #553c9a;
    }
  }


  @keyframes connectLine {
    0% {
      height: 0;
      opacity: 1; /* Maintain full opacity throughout animation */
    }
    100% {
      height: 100%;
      opacity: 1; /* Maintain full opacity throughout animation */
    }
  }


  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }


  /* Ensure the modern loading spinner is properly positioned in the step circle */
  .step-circle.active .modern-loading-spinner {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
  }


  /* Hide loading spinner in failed steps */
  .step-circle.failed .modern-loading-spinner {
    display: none;
  }


  /* Removed pulseLine animation to prevent opacity changes during transitions */


  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // FEATURE 1: Processing pulse animation for non-collapsible steps
  @keyframes processing-pulse {
    0%, 100% {
      opacity: 1; /* Remove low opacity - maintain full visibility */
      transform: scale(1);
    }
    50% {
      opacity: 1; /* Remove low opacity - maintain full visibility */
      transform: scale(1.05);
    }
  }

  // Blur-to-focus expand animation - super fluid and quick
  @keyframes expandBlurToFocus {
    0% {
      filter: blur(4px);
      opacity: 0;
      max-height: 0;
      margin-top: 0;
      margin-bottom: 0;
      transform: translateY(-10px);
    }
    30% {
      filter: blur(2px);
      opacity: 0.3;
      max-height: 150px;
      transform: translateY(-5px);
    }
    70% {
      filter: blur(0.5px);
      opacity: 0.8;
      max-height: 300px;
      transform: translateY(0);
    }
    100% {
      filter: blur(0px);
      opacity: 1;
      max-height: none;
      margin-top: initial;
      margin-bottom: 8px;
      transform: translateY(0);
    }
  }

  // Sharp-to-blur collapse animation - super fluid and quick
  @keyframes collapseSharpToBlur {
    0% {
      filter: blur(0px);
      opacity: 1;
      max-height: none;
      margin-top: initial;
      margin-bottom: 8px;
      transform: translateY(0);
    }
    30% {
      filter: blur(0.5px);
      opacity: 0.8;
      max-height: 200px;
      transform: translateY(-2px);
    }
    70% {
      filter: blur(2px);
      opacity: 0.3;
      max-height: 50px;
      transform: translateY(-5px);
    }
    100% {
      filter: blur(4px);
      opacity: 0;
      max-height: 0;
      margin-top: 0;
      margin-bottom: 0;
      transform: translateY(-10px);
    }
  }


  /* Fluid typewriter animations without cursor */
  .step-title.typing {
    /* Smooth text appearance animation */
    .step-title-text {
      transition: all 0.1s ease-out;
    }
  }

  .step-description.typing {
    ::ng-deep p {
      display: inline;
      /* Smooth text flow */
      transition: all 0.05s ease-out;
    }
  }
}
